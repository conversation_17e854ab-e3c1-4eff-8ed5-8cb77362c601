'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Image from 'next/image';
import { auth, db } from '../../lib/firebase';
import { createUserWithEmailAndPassword } from 'firebase/auth';
import { doc, setDoc, GeoPoint } from 'firebase/firestore';
import { useAuthState } from 'react-firebase-hooks/auth';
import { geohashForLocation } from 'geofire-common';

// OpenStreetMap Component using Leaflet
const MapComponent = ({ onLocationSelect, initialLocation }: {
  onLocationSelect: (location: { lat: number; lng: number }) => void;
  initialLocation?: { lat: number; lng: number } | null;
}) => {
  const mapRef = React.useRef<HTMLDivElement>(null);
  const mapInstanceRef = React.useRef<any>(null);
  const markerRef = React.useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState(initialLocation || { lat: 10.3157, lng: 123.8854 });

  useEffect(() => {
    const initMap = () => {
      console.log('Initializing OpenStreetMap...');
      if (!mapRef.current || !window.L) {
        console.log('Map ref or Leaflet not available');
        return;
      }

      try {
        // Create map
        const map = window.L.map(mapRef.current).setView([selectedLocation.lat, selectedLocation.lng], 15);

        // Add OpenStreetMap tiles
        window.L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
          maxZoom: 19
        }).addTo(map);

        mapInstanceRef.current = map;

        // Create custom marker icon
        const customIcon = window.L.divIcon({
          html: '<div style="background-color: #ef4444; width: 20px; height: 20px; border-radius: 50%; border: 3px solid white; box-shadow: 0 2px 4px rgba(0,0,0,0.3);"></div>',
          className: 'custom-marker',
          iconSize: [20, 20],
          iconAnchor: [10, 10]
        });

        // Create marker
        const marker = window.L.marker([selectedLocation.lat, selectedLocation.lng], {
          icon: customIcon,
          draggable: true
        }).addTo(map);

        markerRef.current = marker;

        // Add event listeners
        marker.on('dragend', (e: any) => {
          const position = e.target.getLatLng();
          const newLocation = {
            lat: position.lat,
            lng: position.lng
          };
          setSelectedLocation(newLocation);
          onLocationSelect(newLocation);
          console.log('Marker dragged to:', newLocation);
        });

        map.on('click', (e: any) => {
          const newLocation = {
            lat: e.latlng.lat,
            lng: e.latlng.lng
          };
          marker.setLatLng([newLocation.lat, newLocation.lng]);
          setSelectedLocation(newLocation);
          onLocationSelect(newLocation);
          console.log('Map clicked at:', newLocation);
        });

        console.log('OpenStreetMap initialized successfully');
        setIsLoading(false);
        setError(null);
      } catch (err) {
        console.error('Error initializing map:', err);
        setError('Failed to initialize map');
        setIsLoading(false);
      }
    };

    const loadLeaflet = () => {
      // Check if Leaflet is already loaded
      if (window.L) {
        console.log('Leaflet already loaded');
        initMap();
        return;
      }

      console.log('Loading Leaflet...');

      // Load Leaflet CSS
      const cssLink = document.createElement('link');
      cssLink.rel = 'stylesheet';
      cssLink.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
      cssLink.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
      cssLink.crossOrigin = '';
      document.head.appendChild(cssLink);

      // Load Leaflet JS
      const script = document.createElement('script');
      script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
      script.integrity = 'sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=';
      script.crossOrigin = '';
      script.async = true;

      script.onload = () => {
        console.log('Leaflet loaded successfully');
        // Small delay to ensure everything is ready
        setTimeout(initMap, 100);
      };

      script.onerror = (err) => {
        console.error('Failed to load Leaflet:', err);
        setError('Failed to load map library. Please check your internet connection.');
        setIsLoading(false);
      };

      document.head.appendChild(script);
    };

    loadLeaflet();
  }, []);

  // Update location when initialLocation changes
  useEffect(() => {
    if (initialLocation && mapInstanceRef.current && markerRef.current) {
      setSelectedLocation(initialLocation);
      mapInstanceRef.current.setView([initialLocation.lat, initialLocation.lng], 15);
      markerRef.current.setLatLng([initialLocation.lat, initialLocation.lng]);
    }
  }, [initialLocation]);

  if (error) {
    return (
      <div className="space-y-4">
        <div className="h-64 bg-yellow-50 border border-yellow-200 rounded-lg flex items-center justify-center">
          <div className="text-center p-4">
            <i className="fas fa-map-marker-alt text-yellow-600 text-3xl mb-3"></i>
            <p className="text-yellow-800 font-medium mb-2">Map temporarily unavailable</p>
            <p className="text-sm text-yellow-700 mb-3">We'll use Cebu City as your default location</p>
            <p className="text-xs text-yellow-600">You can update your exact location later in your dashboard</p>
          </div>
        </div>

        {/* Manual Location Input */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-800 mb-3">
            <i className="fas fa-edit mr-2"></i>
            Or enter your location manually:
          </h4>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <label className="block text-xs font-medium text-blue-700 mb-1">Latitude</label>
              <input
                type="number"
                step="any"
                value={selectedLocation.lat}
                onChange={(e) => {
                  const newLat = parseFloat(e.target.value) || 10.3157;
                  const newLocation = { lat: newLat, lng: selectedLocation.lng };
                  setSelectedLocation(newLocation);
                  onLocationSelect(newLocation);
                }}
                className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="10.3157"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-blue-700 mb-1">Longitude</label>
              <input
                type="number"
                step="any"
                value={selectedLocation.lng}
                onChange={(e) => {
                  const newLng = parseFloat(e.target.value) || 123.8854;
                  const newLocation = { lat: selectedLocation.lat, lng: newLng };
                  setSelectedLocation(newLocation);
                  onLocationSelect(newLocation);
                }}
                className="w-full px-2 py-1 text-sm border border-blue-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
                placeholder="123.8854"
              />
            </div>
          </div>
          <p className="text-xs text-blue-600 mt-2">
            <i className="fas fa-info-circle mr-1"></i>
            You can find your coordinates using Google Maps on your phone or computer
          </p>
        </div>

        {/* Quick Location Presets */}
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">
            <i className="fas fa-map-pin mr-2"></i>
            Quick location presets:
          </h4>
          <div className="grid grid-cols-2 gap-2">
            <button
              type="button"
              onClick={() => {
                const cebuLocation = { lat: 10.3157, lng: 123.8854 };
                setSelectedLocation(cebuLocation);
                onLocationSelect(cebuLocation);
              }}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 text-left"
            >
              <div className="font-medium">Cebu City</div>
              <div className="text-xs text-gray-500">10.3157, 123.8854</div>
            </button>
            <button
              type="button"
              onClick={() => {
                const manilaLocation = { lat: 14.5995, lng: 120.9842 };
                setSelectedLocation(manilaLocation);
                onLocationSelect(manilaLocation);
              }}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 text-left"
            >
              <div className="font-medium">Manila</div>
              <div className="text-xs text-gray-500">14.5995, 120.9842</div>
            </button>
            <button
              type="button"
              onClick={() => {
                const davaoLocation = { lat: 7.1907, lng: 125.4553 };
                setSelectedLocation(davaoLocation);
                onLocationSelect(davaoLocation);
              }}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 text-left"
            >
              <div className="font-medium">Davao City</div>
              <div className="text-xs text-gray-500">7.1907, 125.4553</div>
            </button>
            <button
              type="button"
              onClick={() => {
                const iloiloLocation = { lat: 10.7202, lng: 122.5621 };
                setSelectedLocation(iloiloLocation);
                onLocationSelect(iloiloLocation);
              }}
              className="px-3 py-2 text-sm bg-white border border-gray-300 rounded hover:bg-gray-50 text-left"
            >
              <div className="font-medium">Iloilo City</div>
              <div className="text-xs text-gray-500">10.7202, 122.5621</div>
            </button>
          </div>
        </div>

        {/* Current Selection Display */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-3">
          <div className="flex items-center space-x-2">
            <i className="fas fa-check-circle text-green-600"></i>
            <div>
              <p className="text-sm font-medium text-green-800">Selected Location:</p>
              <p className="text-xs text-green-700">
                Latitude: {selectedLocation.lat.toFixed(6)}, Longitude: {selectedLocation.lng.toFixed(6)}
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="h-64 bg-blue-50 border border-blue-200 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-3"></div>
          <p className="text-blue-700 font-medium">Loading OpenStreetMap...</p>
          <p className="text-sm text-blue-600 mt-1">Fast, free, and reliable mapping</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div
        ref={mapRef}
        className="h-64 w-full rounded-lg border-2 border-gray-300 bg-gray-100"
        style={{ minHeight: '256px' }}
      ></div>
      <div className="bg-green-50 border border-green-200 rounded p-3">
        <div className="flex items-start space-x-2">
          <i className="fas fa-map-marked-alt text-green-600 mt-0.5"></i>
          <div>
            <p className="text-sm text-green-800 font-medium">
              <i className="fas fa-leaf mr-1"></i>
              OpenStreetMap - Free & Open Source
            </p>
            <ul className="text-xs text-green-700 mt-1 space-y-1">
              <li>• Click anywhere on the map to place the red marker</li>
              <li>• Drag the red marker to fine-tune your exact location</li>
              <li>• Zoom in/out using the + and - buttons</li>
              <li>• The marker shows where your barbershop will appear to clients</li>
            </ul>
          </div>
        </div>
      </div>
      {selectedLocation && (
        <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
          <div className="flex items-center justify-between">
            <div>
              <strong>Selected coordinates:</strong> {selectedLocation.lat.toFixed(6)}, {selectedLocation.lng.toFixed(6)}
            </div>
            <div className="text-green-600">
              <i className="fas fa-check-circle mr-1"></i>
              Location set
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default function SignupPage() {
  // Barbershop information
  const [name, setName] = useState('');
  const [phone, setPhone] = useState('');
  const [location, setLocation] = useState<{ lat: number; lng: number }>({ lat: 10.3157, lng: 123.8854 });

  // Account information
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  // UI state
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [step, setStep] = useState(1);
  const router = useRouter();

  // Check if user is already logged in
  const [user, userLoading] = useAuthState(auth);

  // Redirect to dashboard if already logged in
  useEffect(() => {
    if (user && !userLoading) {
      router.push('/dashboard');
    }
  }, [user, userLoading, router]);

  const handleSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);

    // Validate required fields
    if (!name || !phone || !email || !password || !confirmPassword) {
      setError('Please fill in all required fields');
      return;
    }

    // Validate password match
    if (password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    // Validate password strength
    if (password.length < 6) {
      setError('Password must be at least 6 characters');
      return;
    }

    setLoading(true);

    try {
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;

      // Generate geohash for location
      const geohash = geohashForLocation([location.lat, location.lng]);

      // Create barbershop profile in Firestore
      const barbershopId = user.uid;
      const createdAt = Date.now(); // Milliseconds since epoch

      await setDoc(doc(db, 'barbershops', barbershopId), {
        barbershopId, // UID as a field for easy reference
        name,
        phone,
        email,
        loc: {
          coordinates: new GeoPoint(location.lat, location.lng),
          geohash,
        },
        isOpen: false, // Default value
        barbers: [], // Default empty array
        services: [], // Default empty array
        createdAt, // Timestamp in milliseconds
        status: 'active',
      });

      // Get the ID token
      const idToken = await userCredential.user.getIdToken();

      // Store the token in a cookie for the middleware to use
      document.cookie = `firebaseToken=${idToken}; path=/; max-age=${60 * 60 * 24 * 5}`; // 5 days

      // Redirect will happen automatically due to the useEffect above
    } catch (error) {
      setError((error as Error).message);
      setLoading(false);
    }
  };

  const nextStep = () => {
    if (step === 1) {
      // Validate first step
      if (!name || !phone) {
        setError('Please fill in all required fields');
        return;
      }
    }
    setError(null);
    setStep(step + 1);
  };

  const prevStep = () => {
    setError(null);
    setStep(step - 1);
  };



  return (
    <div className="min-h-screen bg-gray-50">
      {/* Background Pattern */}
      <div className="fixed inset-0 z-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: 'url("/images/homepagebg.jpg")',
          backgroundSize: 'cover',
          filter: 'contrast(120%) brightness(105%)'
        }}></div>
      </div>

      {/* Navigation */}
      <nav className="bg-white shadow-sm sticky top-0 z-50">
        <div className="container-custom py-4">
          <div className="flex justify-between items-center">
            <Link href="/" className="flex items-center">
              <Image src="/images/mainalotlogo.png" alt="ALOT Logo" width={36} height={36} className="object-contain" />
              <span className="ml-2 font-semibold text-gray-900">ALOT</span>
            </Link>
            <div>
              <Link href="/" className="text-gray-700 hover:text-black font-medium transition-colors duration-200">
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Home
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="container-custom py-12 relative z-10">
        <div className="max-w-3xl mx-auto">
          <div className="text-center mb-8 animate-fadeIn">
            <h1 className="text-3xl font-bold text-gray-900 mb-3">Register Your Barbershop</h1>
            <p className="text-gray-600 max-w-xl mx-auto">
              Join our platform and connect with customers through our mobile app
            </p>
          </div>



          {/* Progress Steps */}
          <div className="mb-10 animate-fadeIn" style={{ animationDelay: '0.1s' }}>
            <div className="flex items-center justify-between">
              <div className="flex-1 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div
                  className="h-full bg-black transition-all duration-500 ease-in-out"
                  style={{ width: step >= 2 ? '100%' : '50%' }}
                ></div>
              </div>
            </div>
            <div className="flex justify-between mt-3">
              <div className="text-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2 shadow-sm transition-all duration-300 ${step >= 1 ? 'bg-black text-white' : 'bg-gray-200 text-gray-600'}`}>
                  1
                </div>
                <span className="text-sm font-medium text-gray-800">Shop Info</span>
              </div>
              <div className="text-center">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center mx-auto mb-2 shadow-sm transition-all duration-300 ${step >= 2 ? 'bg-black text-white' : 'bg-gray-200 text-gray-600'}`}>
                  2
                </div>
                <span className="text-sm font-medium text-gray-800">Account</span>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md overflow-hidden border border-gray-100 animate-fadeIn" style={{ animationDelay: '0.2s' }}>
            <div className="px-6 py-4 border-b border-gray-100">
              <h2 className="text-xl font-semibold text-gray-900">
                {step === 1 ? 'Barbershop Information' : 'Account Information'}
              </h2>
            </div>
            <div className="p-6">
              {error && (
                <div className="bg-red-50 border-l-4 border-red-500 text-red-700 p-4 rounded mb-6 text-sm animate-fadeIn">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <i className="fas fa-exclamation-circle text-red-500 mr-2"></i>
                    </div>
                    <p>{error}</p>
                  </div>
                </div>
              )}

              <form onSubmit={handleSignup}>
                {/* Step 1: Barbershop Information */}
                {step === 1 && (
                  <div className="space-y-6">
                    <div className="mb-5">
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">Barbershop Name*</label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fas fa-store text-gray-400 group-hover:text-black transition-colors duration-200"></i>
                        </div>
                        <input
                          id="name"
                          type="text"
                          value={name}
                          onChange={(e) => setName(e.target.value)}
                          className="w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black transition-colors duration-200"
                          required
                          placeholder="e.g. Classic Cuts Barbershop"
                        />
                        {name && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="mb-5">
                      <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">Contact Number*</label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fas fa-phone text-gray-400 group-hover:text-black transition-colors duration-200"></i>
                        </div>
                        <input
                          id="phone"
                          type="tel"
                          value={phone}
                          onChange={(e) => setPhone(e.target.value)}
                          className="w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black transition-colors duration-200"
                          required
                          placeholder="e.g. 09123456789"
                        />
                        {phone && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                      <p className="mt-1 text-xs text-gray-500">This number will be visible to clients for booking inquiries</p>
                    </div>

                    {/* Map Component */}
                    <div className="mb-5">
                      <label className="block text-sm font-medium text-gray-700 mb-1">Barbershop Location*</label>
                      <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                        <MapComponent onLocationSelect={setLocation} initialLocation={location} />
                      </div>
                      <p className="mt-2 text-xs text-gray-500">
                        Click on the map or drag the marker to set your exact barbershop location
                      </p>
                    </div>

                    <div className="flex justify-end pt-4">
                      <button
                        type="button"
                        onClick={nextStep}
                        className="inline-flex items-center px-4 py-2.5 bg-black text-white text-sm font-medium rounded-md shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
                      >
                        Continue to Account Setup
                        <i className="fas fa-arrow-right ml-2"></i>
                      </button>
                    </div>
                  </div>
                )}

                {/* Step 2: Account Information */}
                {step === 2 && (
                  <div className="space-y-6">
                    <div className="mb-5">
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">Email Address*</label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fas fa-envelope text-gray-400 group-hover:text-black transition-colors duration-200"></i>
                        </div>
                        <input
                          id="email"
                          type="email"
                          value={email}
                          onChange={(e) => setEmail(e.target.value)}
                          className="w-full pl-10 pr-3 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black transition-colors duration-200"
                          required
                          placeholder="e.g. <EMAIL>"
                        />
                        {email && (
                          <div className="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                      <p className="mt-1 text-xs text-gray-500">You'll use this email to log in to your account</p>
                    </div>

                    <div className="mb-5">
                      <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">Password*</label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fas fa-lock text-gray-400 group-hover:text-black transition-colors duration-200"></i>
                        </div>
                        <input
                          id="password"
                          type={showPassword ? 'text' : 'password'}
                          value={password}
                          onChange={(e) => setPassword(e.target.value)}
                          className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black transition-colors duration-200"
                          required
                          placeholder="••••••••"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-black transition-colors duration-200"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <i className="fas fa-eye-slash"></i>
                          ) : (
                            <i className="fas fa-eye"></i>
                          )}
                        </button>
                        {password && password.length >= 6 && (
                          <div className="absolute inset-y-0 right-10 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                      <p className="mt-1 text-xs text-gray-500">Must be at least 6 characters</p>
                    </div>

                    <div className="mb-5">
                      <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">Confirm Password*</label>
                      <div className="relative group">
                        <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <i className="fas fa-lock text-gray-400 group-hover:text-black transition-colors duration-200"></i>
                        </div>
                        <input
                          id="confirmPassword"
                          type={showPassword ? 'text' : 'password'}
                          value={confirmPassword}
                          onChange={(e) => setConfirmPassword(e.target.value)}
                          className="w-full pl-10 pr-10 py-2.5 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-1 focus:ring-black focus:border-black transition-colors duration-200"
                          required
                          placeholder="••••••••"
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-black transition-colors duration-200"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <i className="fas fa-eye-slash"></i>
                          ) : (
                            <i className="fas fa-eye"></i>
                          )}
                        </button>
                        {confirmPassword && password === confirmPassword && (
                          <div className="absolute inset-y-0 right-10 pr-3 flex items-center pointer-events-none">
                            <i className="fas fa-check text-green-500"></i>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex justify-between pt-4">
                      <button
                        type="button"
                        onClick={prevStep}
                        className="inline-flex items-center px-4 py-2.5 bg-white text-gray-700 text-sm font-medium rounded-md border border-gray-300 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200"
                      >
                        <i className="fas fa-arrow-left mr-2"></i>
                        Back
                      </button>
                      <button
                        type="submit"
                        disabled={loading}
                        className="inline-flex items-center px-4 py-2.5 bg-black text-white text-sm font-medium rounded-md shadow-sm hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {loading ? (
                          <span className="flex items-center">
                            <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            Registering...
                          </span>
                        ) : (
                          <>
                            Register Barbershop
                            <i className="fas fa-check ml-2"></i>
                          </>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </form>
            </div>
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-100 text-center">
              <p className="text-sm text-gray-600">
                Already have an account? <Link href="/" className="text-black hover:underline font-medium transition-colors">Login</Link>
              </p>
            </div>
          </div>

          <div className="mt-6 text-center text-gray-500 text-sm animate-fadeIn" style={{ animationDelay: '0.3s' }}>
            <p>By registering, you agree to our <a href="#" className="text-black hover:underline transition-colors">Terms of Service</a> and <a href="#" className="text-black hover:underline transition-colors">Privacy Policy</a>.</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 py-6 mt-12 relative z-10">
        <div className="container-custom">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center mb-4 md:mb-0">
              <Image src="/images/mainalotlogo.png" alt="ALOT Logo" width={32} height={32} className="object-contain" />
              <span className="ml-2 font-semibold text-gray-900">ALOT</span>
            </div>
            <div className="text-gray-600 text-sm">
              &copy; {new Date().getFullYear()} ALOT. All rights reserved.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
